package com.miner.strategy.backtest.core;

import com.miner.strategy.backtest.core.event.EventBus;
import com.miner.strategy.backtest.core.event.MarketDataEvent;
import com.miner.strategy.backtest.core.event.SignalEvent;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;

/**
 * 策略执行调度器
 * 负责管理和调度策略的执行
 * 
 * <AUTHOR>
 */
@Slf4j
public class StrategyExecutor {
    
    private final Map<String, TradingStrategy> strategies;
    private final EventBus eventBus;
    private final ExecutorService executorService;
    private final boolean parallelExecution;
    
    public StrategyExecutor(EventBus eventBus) {
        this(eventBus, false);
    }
    
    public StrategyExecutor(EventBus eventBus, boolean parallelExecution) {
        this.strategies = new ConcurrentHashMap<>();
        this.eventBus = eventBus;
        this.parallelExecution = parallelExecution;
        this.executorService = parallelExecution ? Executors.newCachedThreadPool() : null;
    }
    
    /**
     * 注册策略
     * 
     * @param strategy 交易策略
     */
    public void registerStrategy(TradingStrategy strategy) {
        strategies.put(strategy.getStrategyName(), strategy);
        log.info("注册策略: {} (版本: {})", strategy.getStrategyName(), strategy.getVersion());
    }
    
    /**
     * 取消注册策略
     * 
     * @param strategyName 策略名称
     */
    public void unregisterStrategy(String strategyName) {
        TradingStrategy removed = strategies.remove(strategyName);
        if (removed != null) {
            log.info("取消注册策略: {}", strategyName);
        }
    }
    
    /**
     * 获取策略
     * 
     * @param strategyName 策略名称
     * @return 策略实例
     */
    public TradingStrategy getStrategy(String strategyName) {
        return strategies.get(strategyName);
    }
    
    /**
     * 执行策略处理市场数据
     * 
     * @param marketDataEvent 市场数据事件
     */
    public void executeStrategies(MarketDataEvent marketDataEvent) {
        if (strategies.isEmpty()) {
            return;
        }
        
        if (parallelExecution) {
            // 并行执行策略
            executeStrategiesParallel(marketDataEvent);
        } else {
            // 串行执行策略
            executeStrategiesSequential(marketDataEvent);
        }
    }
    
    /**
     * 串行执行策略
     * 
     * @param marketDataEvent 市场数据事件
     */
    private void executeStrategiesSequential(MarketDataEvent marketDataEvent) {
        for (TradingStrategy strategy : strategies.values()) {
            try {
                if (strategy.getState() == TradingStrategy.StrategyState.ACTIVE) {
                    List<SignalEvent> signals = strategy.onMarketData(marketDataEvent);
                    publishSignals(signals);
                }
            } catch (Exception e) {
                log.error("策略 {} 执行时发生异常: {}", strategy.getStrategyName(), e.getMessage(), e);
            }
        }
    }
    
    /**
     * 并行执行策略
     * 
     * @param marketDataEvent 市场数据事件
     */
    private void executeStrategiesParallel(MarketDataEvent marketDataEvent) {
        List<Future<?>> futures = strategies.values().stream()
            .filter(strategy -> strategy.getState() == TradingStrategy.StrategyState.ACTIVE)
            .map(strategy -> executorService.submit(() -> {
                try {
                    List<SignalEvent> signals = strategy.onMarketData(marketDataEvent);
                    publishSignals(signals);
                } catch (Exception e) {
                    log.error("策略 {} 执行时发生异常: {}", strategy.getStrategyName(), e.getMessage(), e);
                }
            }))
            .toList();
        
        // 等待所有策略执行完成
        for (Future<?> future : futures) {
            try {
                future.get();
            } catch (Exception e) {
                log.error("等待策略执行完成时发生异常: {}", e.getMessage(), e);
            }
        }
    }
    
    /**
     * 发布信号事件
     * 
     * @param signals 信号列表
     */
    private void publishSignals(List<SignalEvent> signals) {
        if (signals != null && !signals.isEmpty()) {
            for (SignalEvent signal : signals) {
                eventBus.publish(signal);
            }
        }
    }
    
    /**
     * 暂停所有策略
     */
    public void pauseAllStrategies() {
        strategies.values().forEach(strategy -> {
            // 这里需要策略实现暂停逻辑
            log.info("暂停策略: {}", strategy.getStrategyName());
        });
    }
    
    /**
     * 恢复所有策略
     */
    public void resumeAllStrategies() {
        strategies.values().forEach(strategy -> {
            // 这里需要策略实现恢复逻辑
            log.info("恢复策略: {}", strategy.getStrategyName());
        });
    }
    
    /**
     * 重置所有策略
     */
    public void resetAllStrategies() {
        strategies.values().forEach(TradingStrategy::reset);
        log.info("已重置所有策略");
    }
    
    /**
     * 获取策略数量
     * 
     * @return 策略数量
     */
    public int getStrategyCount() {
        return strategies.size();
    }
    
    /**
     * 获取活跃策略数量
     * 
     * @return 活跃策略数量
     */
    public long getActiveStrategyCount() {
        return strategies.values().stream()
            .filter(strategy -> strategy.getState() == TradingStrategy.StrategyState.ACTIVE)
            .count();
    }
    
    /**
     * 关闭执行器
     */
    public void shutdown() {
        if (executorService != null) {
            executorService.shutdown();
        }
        strategies.clear();
        log.info("策略执行器已关闭");
    }
}
