package com.miner.strategy.backtest.core;

import com.miner.strategy.backtest.core.impl.EMARetraceStrategyAdapter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 策略注册器
 * 负责自动注册所有可用的策略
 * 
 * <AUTHOR>
 */
@Component
@Slf4j
public class StrategyRegistry {
    
    @PostConstruct
    public void registerStrategies() {
        log.info("开始注册策略...");
        
        StrategyFactory factory = StrategyFactory.getInstance();
        
        // 注册EMA回调策略
        factory.registerStrategy(
            "EMARetraceStrategy",
            EMARetraceStrategyAdapter::new,
            "EMA回调策略 - 基于EMA多头排列和RSI的回调入场策略",
            "2.0.0"
        );
        
        // 可以在这里注册更多策略
        // factory.registerStrategy("AnotherStrategy", AnotherStrategyAdapter::new, "描述", "版本");
        
        log.info("策略注册完成，共注册 {} 个策略", factory.getRegisteredStrategyCount());
        
        // 输出已注册的策略信息
        for (String strategyName : factory.getRegisteredStrategyNames()) {
            StrategyFactory.StrategyDescriptor descriptor = factory.getStrategyDescriptor(strategyName);
            log.info("已注册策略: {} - {} (版本: {})", 
                    strategyName, descriptor.getDescription(), descriptor.getVersion());
        }
    }
}
