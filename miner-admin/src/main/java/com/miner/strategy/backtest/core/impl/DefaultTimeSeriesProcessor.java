package com.miner.strategy.backtest.core.impl;

import com.miner.strategy.backtest.core.TimeSeriesProcessor;
import com.miner.system.indicator.KLineEntity;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 默认时间序列处理器实现
 *
 * <AUTHOR>
 */
@Slf4j
public class DefaultTimeSeriesProcessor implements TimeSeriesProcessor {

    private Map<String, Map<String, List<KLineEntity>>> separatedData;
    private List<Long> allTimestamps;
    private DataStatistics dataStatistics;

    @Override
    public void initialize(Map<String, List<KLineEntity>> historicalData) {
        log.info("初始化时间序列处理器...");

        // 分离不同时间框架的数据
        this.separatedData = separateDataByTimeframe(historicalData);

        // 生成所有时间戳
        this.allTimestamps = generateAllTimestamps();

        // 计算数据统计
        this.dataStatistics = calculateDataStatistics();

        log.info("时间序列处理器初始化完成，共处理 {} 个交易对，{} 个时间点",
                separatedData.size(), allTimestamps.size());
    }

    @Override
    public List<Long> getAllTimestamps() {
        return new ArrayList<>(allTimestamps);
    }

    @Override
    public KLineEntity getKLineAt(String symbol, long timestamp, String timeframe) {
        Map<String, List<KLineEntity>> timeframeData = separatedData.get(symbol);
        if (timeframeData == null) {
            return null;
        }

        List<KLineEntity> klines = timeframeData.get(timeframe);
        if (klines == null) {
            return null;
        }

        // 使用二分查找提高效率
        return binarySearchKLine(klines, timestamp);
    }

    @Override
    public List<KLineEntity> getKLineRange(String symbol, long startTime, long endTime, String timeframe) {
        Map<String, List<KLineEntity>> timeframeData = separatedData.get(symbol);
        if (timeframeData == null) {
            return Collections.emptyList();
        }

        List<KLineEntity> klines = timeframeData.get(timeframe);
        if (klines == null) {
            return Collections.emptyList();
        }

        return klines.stream()
            .filter(kline -> kline.getTs() >= startTime && kline.getTs() <= endTime)
            .collect(Collectors.toList());
    }

    @Override
    public List<KLineEntity> getLatestKLines(String symbol, long timestamp, int count, String timeframe) {
        Map<String, List<KLineEntity>> timeframeData = separatedData.get(symbol);
        if (timeframeData == null) {
            return Collections.emptyList();
        }

        List<KLineEntity> klines = timeframeData.get(timeframe);
        if (klines == null) {
            return Collections.emptyList();
        }

        List<KLineEntity> result = new ArrayList<>();
        for (int i = klines.size() - 1; i >= 0 && result.size() < count; i--) {
            KLineEntity kline = klines.get(i);
            if (kline.getTs() <= timestamp) {
                result.add(0, kline); // 插入到开头保持时间顺序
            }
        }

        return result;
    }

    @Override
    public DataIntegrityResult checkDataIntegrity(String symbol, String timeframe) {
        Map<String, List<KLineEntity>> timeframeData = separatedData.get(symbol);
        if (timeframeData == null) {
            return new DataIntegrityResult(false, 0.0, Collections.emptyList(),
                                         "交易对 " + symbol + " 不存在");
        }

        List<KLineEntity> klines = timeframeData.get(timeframe);
        if (klines == null || klines.isEmpty()) {
            return new DataIntegrityResult(false, 0.0, Collections.emptyList(),
                                         "时间框架 " + timeframe + " 数据不存在");
        }

        // 检查数据连续性
        List<Long> missingTimestamps = new ArrayList<>();
        long timeframeMs = getTimeframeMilliseconds(timeframe);

        for (int i = 1; i < klines.size(); i++) {
            long expectedTime = klines.get(i - 1).getTs() + timeframeMs;
            long actualTime = klines.get(i).getTs();

            if (actualTime != expectedTime) {
                // 找出缺失的时间戳
                long currentTime = expectedTime;
                while (currentTime < actualTime) {
                    missingTimestamps.add(currentTime);
                    currentTime += timeframeMs;
                }
            }
        }

        double completeness = (double) klines.size() / (klines.size() + missingTimestamps.size());
        boolean complete = missingTimestamps.isEmpty();
        String message = complete ? "数据完整" :
                        String.format("缺失 %d 个数据点，完整性 %.2f%%",
                                    missingTimestamps.size(), completeness * 100);

        return new DataIntegrityResult(complete, completeness, missingTimestamps, message);
    }

    @Override
    public DataStatistics getDataStatistics() {
        return dataStatistics;
    }

    /**
     * 分离不同时间框架的数据
     */
    private Map<String, Map<String, List<KLineEntity>>> separateDataByTimeframe(
            Map<String, List<KLineEntity>> historicalData) {

        Map<String, Map<String, List<KLineEntity>>> result = new HashMap<>();

        for (Map.Entry<String, List<KLineEntity>> entry : historicalData.entrySet()) {
            String key = entry.getKey();
            List<KLineEntity> klines = entry.getValue();

            // 解析交易对和时间框架
            String[] parts = key.split("_");
            if (parts.length >= 2) {
                String symbol = parts[0];
                String timeframe = parts[1];

                result.computeIfAbsent(symbol, k -> new HashMap<>())
                      .put(timeframe, new ArrayList<>(klines));
            }
        }

        // 对每个时间框架的数据按时间排序
        for (Map<String, List<KLineEntity>> timeframeData : result.values()) {
            for (List<KLineEntity> klines : timeframeData.values()) {
                klines.sort(Comparator.comparing(KLineEntity::getTs));
            }
        }

        return result;
    }

    /**
     * 生成所有时间戳
     */
    private List<Long> generateAllTimestamps() {
        Set<Long> timestampSet = new HashSet<>();

        for (Map<String, List<KLineEntity>> timeframeData : separatedData.values()) {
            for (List<KLineEntity> klines : timeframeData.values()) {
                for (KLineEntity kline : klines) {
                    timestampSet.add(kline.getTs());
                }
            }
        }

        List<Long> timestamps = new ArrayList<>(timestampSet);
        timestamps.sort(Long::compare);
        return timestamps;
    }

    /**
     * 计算数据统计
     */
    private DataStatistics calculateDataStatistics() {
        Map<String, Integer> symbolCounts = new HashMap<>();
        Map<String, Long> timeRanges = new HashMap<>();
        long totalDataPoints = 0;
        double totalCompleteness = 0.0;
        int completenessCount = 0;

        for (Map.Entry<String, Map<String, List<KLineEntity>>> entry : separatedData.entrySet()) {
            String symbol = entry.getKey();
            Map<String, List<KLineEntity>> timeframeData = entry.getValue();

            int symbolTotal = 0;
            long minTime = Long.MAX_VALUE;
            long maxTime = Long.MIN_VALUE;

            for (Map.Entry<String, List<KLineEntity>> tfEntry : timeframeData.entrySet()) {
                String timeframe = tfEntry.getKey();
                List<KLineEntity> klines = tfEntry.getValue();

                symbolTotal += klines.size();
                totalDataPoints += klines.size();

                if (!klines.isEmpty()) {
                    minTime = Math.min(minTime, klines.get(0).getTs());
                    maxTime = Math.max(maxTime, klines.get(klines.size() - 1).getTs());

                    // 计算完整性
                    DataIntegrityResult integrity = checkDataIntegrity(symbol, timeframe);
                    totalCompleteness += integrity.getCompleteness();
                    completenessCount++;
                }
            }

            symbolCounts.put(symbol, symbolTotal);
            if (minTime != Long.MAX_VALUE) {
                timeRanges.put(symbol, maxTime - minTime);
            }
        }

        double averageCompleteness = completenessCount > 0 ? totalCompleteness / completenessCount : 0.0;

        return new DataStatistics(symbolCounts, timeRanges, totalDataPoints, averageCompleteness);
    }

    /**
     * 二分查找K线数据
     */
    private KLineEntity binarySearchKLine(List<KLineEntity> klines, long timestamp) {
        int left = 0;
        int right = klines.size() - 1;

        while (left <= right) {
            int mid = left + (right - left) / 2;
            KLineEntity midKline = klines.get(mid);

            if (midKline.getTs() == timestamp) {
                return midKline;
            } else if (midKline.getTs() < timestamp) {
                left = mid + 1;
            } else {
                right = mid - 1;
            }
        }

        return null;
    }

    /**
     * 获取时间框架对应的毫秒数
     */
    private long getTimeframeMilliseconds(String timeframe) {
        String timeframeLowerCase = timeframe.toLowerCase();
        return switch (timeframeLowerCase) {
            case "1m" -> 60 * 1000L;
            case "5m" -> 5 * 60 * 1000L;
            case "15m" -> 15 * 60 * 1000L;
            case "30m" -> 30 * 60 * 1000L;
            case "1h" -> 60 * 60 * 1000L;
            case "4h" -> 4 * 60 * 60 * 1000L;
            case "1d" -> 24 * 60 * 60 * 1000L;
            default -> 60 * 1000L; // 默认1分钟
        };
    }
}
