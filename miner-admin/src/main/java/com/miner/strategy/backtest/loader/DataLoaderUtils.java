package com.miner.strategy.backtest.loader;

import com.miner.system.domain.Bar;
import com.miner.system.mapper.BarMapper;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * 数据加载工具类
 * 为HistoricalDataLoader提供辅助方法
 */
@Slf4j
public class DataLoaderUtils {

    /**
     * 分批获取Bar数据的兼容方法
     * 如果BarMapper没有getBarsWithLimit方法，则使用getBars方法
     */
    public static List<Bar> getBarsWithLimit(BarMapper barMapper, String symbol, String timeframe, 
                                           String startDate, String endDate, int offset, int limit) {
        try {
            // 尝试调用getBarsWithLimit方法（如果存在）
            return barMapper.getBarsWithLimit(symbol, timeframe, startDate, endDate, offset, limit);
        } catch (Exception e) {
            // 如果方法不存在，使用getBars方法并手动分页
            log.debug("getBarsWithLimit方法不存在，使用getBars方法: {}", e.getMessage());
            
            try {
                List<Bar> allBars = barMapper.getBars(symbol, timeframe, startDate, endDate);
                
                if (allBars == null || allBars.isEmpty()) {
                    return allBars;
                }
                
                // 手动分页
                int fromIndex = Math.min(offset, allBars.size());
                int toIndex = Math.min(offset + limit, allBars.size());
                
                if (fromIndex >= allBars.size()) {
                    return List.of(); // 返回空列表
                }
                
                return allBars.subList(fromIndex, toIndex);
                
            } catch (Exception ex) {
                log.error("获取Bar数据失败", ex);
                return List.of();
            }
        }
    }
    
    /**
     * 检查BarMapper是否支持分页查询
     */
    public static boolean supportsLimitQuery(BarMapper barMapper) {
        try {
            // 尝试调用getBarsWithLimit方法
            barMapper.getClass().getMethod("getBarsWithLimit", 
                String.class, String.class, String.class, String.class, int.class, int.class);
            return true;
        } catch (NoSuchMethodException e) {
            return false;
        }
    }
}
