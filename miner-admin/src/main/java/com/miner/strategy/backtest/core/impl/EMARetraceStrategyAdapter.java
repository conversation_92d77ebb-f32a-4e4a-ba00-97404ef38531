package com.miner.strategy.backtest.core.impl;

import com.miner.strategy.backtest.config.BacktestConfig;
import com.miner.strategy.backtest.core.TradingStrategy;
import com.miner.strategy.backtest.core.event.MarketDataEvent;
import com.miner.strategy.backtest.core.event.SignalEvent;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.*;

/**
 * EMA回调策略适配器
 * 将现有的EMA策略适配到新的策略框架中
 * 
 * <AUTHOR>
 */
@Slf4j
public class EMARetraceStrategyAdapter implements TradingStrategy {
    
    private BacktestConfig config;
    private StrategyState state = StrategyState.INITIALIZED;
    private Map<String, Object> parameters = new HashMap<>();
    
    // 策略状态
    private final Map<String, StrategyContext> symbolContexts = new HashMap<>();
    
    @Override
    public void initialize(BacktestConfig config) {
        this.config = config;
        
        // 设置策略参数
        parameters.put("emaFast", config.getEmaFast());
        parameters.put("emaMid", config.getEmaMid());
        parameters.put("emaSlow", config.getEmaSlow());
        parameters.put("rsiPeriod", config.getRsiPeriod());
        parameters.put("rsiOversold", config.getRsiOversold());
        parameters.put("rsiRising", config.getRsiRising());
        parameters.put("entryScoreThreshold", config.getEntryScoreThreshold());
        parameters.put("atrPeriod", config.getAtrPeriod());
        parameters.put("stopLossMultiplier", config.getStopLossMultiplier());
        parameters.put("takeProfitMultiplier1", config.getTakeProfitMultiplier1());
        parameters.put("takeProfitMultiplier2", config.getTakeProfitMultiplier2());
        
        // 为每个交易对初始化上下文
        for (String symbol : config.getSymbols()) {
            symbolContexts.put(symbol, new StrategyContext(symbol));
        }
        
        state = StrategyState.ACTIVE;
        log.info("EMA回调策略适配器初始化完成，交易对数量: {}", config.getSymbols().size());
    }
    
    @Override
    public List<SignalEvent> onMarketData(MarketDataEvent event) {
        if (state != StrategyState.ACTIVE) {
            return Collections.emptyList();
        }
        
        String symbol = event.getSymbol();
        StrategyContext context = symbolContexts.get(symbol);
        
        if (context == null) {
            return Collections.emptyList();
        }
        
        List<SignalEvent> signals = new ArrayList<>();
        
        try {
            // 更新上下文数据
            context.updateMarketData(event);
            
            // 检查是否有足够的数据进行分析
            if (!context.hasEnoughData()) {
                return signals;
            }
            
            // 执行策略逻辑
            SignalEvent signal = executeStrategyLogic(context, event);
            if (signal != null) {
                signals.add(signal);
            }
            
        } catch (Exception e) {
            log.error("处理交易对 {} 的市场数据时发生异常: {}", symbol, e.getMessage(), e);
        }
        
        return signals;
    }
    
    @Override
    public String getStrategyName() {
        return "EMARetraceStrategy";
    }
    
    @Override
    public String getVersion() {
        return "2.0.0";
    }
    
    @Override
    public String getDescription() {
        return "EMA回调策略 - 基于EMA多头排列和RSI的回调入场策略";
    }
    
    @Override
    public Map<String, Object> getParameters() {
        return new HashMap<>(parameters);
    }
    
    @Override
    public void setParameters(Map<String, Object> parameters) {
        this.parameters.putAll(parameters);
        log.info("更新策略参数: {}", parameters);
    }
    
    @Override
    public ParameterValidationResult validateParameters(Map<String, Object> parameters) {
        List<String> errors = new ArrayList<>();
        
        // 验证EMA参数
        Integer emaFast = (Integer) parameters.get("emaFast");
        Integer emaMid = (Integer) parameters.get("emaMid");
        Integer emaSlow = (Integer) parameters.get("emaSlow");
        
        if (emaFast == null || emaFast <= 0) {
            errors.add("emaFast必须大于0");
        }
        if (emaMid == null || emaMid <= 0) {
            errors.add("emaMid必须大于0");
        }
        if (emaSlow == null || emaSlow <= 0) {
            errors.add("emaSlow必须大于0");
        }
        
        if (emaFast != null && emaMid != null && emaSlow != null) {
            if (emaFast >= emaMid || emaMid >= emaSlow) {
                errors.add("EMA参数必须满足: emaFast < emaMid < emaSlow");
            }
        }
        
        // 验证RSI参数
        Integer rsiPeriod = (Integer) parameters.get("rsiPeriod");
        if (rsiPeriod == null || rsiPeriod <= 0) {
            errors.add("rsiPeriod必须大于0");
        }
        
        Double rsiOversold = (Double) parameters.get("rsiOversold");
        if (rsiOversold == null || rsiOversold < 0 || rsiOversold > 100) {
            errors.add("rsiOversold必须在0-100之间");
        }
        
        boolean valid = errors.isEmpty();
        String message = valid ? "参数验证通过" : "参数验证失败";
        
        return new ParameterValidationResult(valid, message, errors);
    }
    
    @Override
    public void reset() {
        symbolContexts.clear();
        if (config != null) {
            for (String symbol : config.getSymbols()) {
                symbolContexts.put(symbol, new StrategyContext(symbol));
            }
        }
        state = StrategyState.INITIALIZED;
        log.info("策略状态已重置");
    }
    
    @Override
    public StrategyState getState() {
        return state;
    }
    
    /**
     * 执行策略逻辑
     */
    private SignalEvent executeStrategyLogic(StrategyContext context, MarketDataEvent event) {
        // 这里实现具体的EMA回调策略逻辑
        // 暂时返回null，实际实现需要根据现有的EMABacktestEngine逻辑进行适配
        
        // 检查EMA多头排列
        if (!context.isEMABullish()) {
            return null;
        }
        
        // 检查RSI回调条件
        if (!context.isRSIRetrace()) {
            return null;
        }
        
        // 生成买入信号
        BigDecimal price = event.getKlineData().getClose();
        BigDecimal quantity = calculatePositionSize(price);
        
        return new SignalEvent(
            event.getSymbol(),
            SignalEvent.SignalType.BUY,
            price,
            quantity,
            getStrategyName(),
            0.8, // 信号置信度
            event.getTimestamp()
        );
    }
    
    /**
     * 计算仓位大小
     */
    private BigDecimal calculatePositionSize(BigDecimal price) {
        double positionValue = config.getInitialCapital() * config.getPositionSizeRatio();
        return BigDecimal.valueOf(positionValue).divide(price, 8, BigDecimal.ROUND_DOWN);
    }
    
    /**
     * 策略上下文
     * 保存每个交易对的策略状态
     */
    private static class StrategyContext {
        private final String symbol;
        private final List<BigDecimal> prices = new ArrayList<>();
        private final int maxDataPoints = 100; // 保留最近100个数据点
        
        public StrategyContext(String symbol) {
            this.symbol = symbol;
        }
        
        public void updateMarketData(MarketDataEvent event) {
            prices.add(event.getKlineData().getClose());
            
            // 保持数据点数量在限制范围内
            if (prices.size() > maxDataPoints) {
                prices.remove(0);
            }
        }
        
        public boolean hasEnoughData() {
            return prices.size() >= 55; // 需要至少55个数据点计算EMA55
        }
        
        public boolean isEMABullish() {
            // 简化的EMA多头排列检查
            if (prices.size() < 55) {
                return false;
            }
            
            // 这里应该实现真正的EMA计算和多头排列检查
            // 暂时返回true作为示例
            return true;
        }
        
        public boolean isRSIRetrace() {
            // 简化的RSI回调检查
            if (prices.size() < 14) {
                return false;
            }
            
            // 这里应该实现真正的RSI计算和回调检查
            // 暂时返回true作为示例
            return true;
        }
    }
}
